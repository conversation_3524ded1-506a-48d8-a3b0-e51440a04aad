import Link from "next/link";
import { ArrowRight, FileImage, FileText, Palette, Image, Layers, Scissors, Minimize2, Square, File, Crop } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

// Only show popular tools on homepage
const popularTools = [
  {
    name: "Bulk Image Converter",
    href: "/bulk-convert",
    icon: <Layers className="h-6 w-6 text-primary" />,
    description: "Convert multiple images at once. Upload up to 20 images and convert them all with the same settings.",
  },
  {
    name: "Compress Image",
    href: "/compress-image",
    icon: <Minimize2 className="h-6 w-6 text-primary" />,
    description: "Reduce image file sizes significantly while maintaining quality. Supports PNG, JPG, WebP, and more.",
  },
  {
    name: "Resize Image",
    href: "/resize-image",
    icon: <Scissors className="h-6 w-6 text-primary" />,
    description: "Easily change the dimensions (width and height) of your images online.",
  },
  {
    name: "Crop Image",
    href: "/crop-image",
    icon: <Crop className="h-6 w-6 text-primary" />,
    description: "Precisely crop your images with custom dimensions or preset aspect ratios.",
  },
  {
    name: "Favicon Maker",
    href: "/favicon-maker",
    icon: <Palette className="h-6 w-6 text-primary" />,
    description: "Create favicons (website icons) in various formats from your base image.",
  },
  {
    name: "Placeholder Image Maker",
    href: "/placeholder-maker",
    icon: <Square className="h-6 w-6 text-primary" />,
    description: "Generate simple placeholder images with custom dimensions and text.",
  },
  {
    name: "PNG to PDF",
    href: "/convert/png-to-pdf",
    icon: <FileText className="h-6 w-6 text-primary" />,
    description: "Convert PNG images to PDF documents, perfect for sharing or archiving."
  },
  {
    name: "Image Compositor",
    href: "/composite-images",
    icon: <Layers className="h-6 w-6 text-primary" />,
    description: "Merge multiple images together by overlaying them onto a base image."
  },
];

export function ToolsSection() {
  return (
    <section id="tools" className="w-full">
        {/* Section Header */}
        <div className="flex flex-col items-center mb-16">
          <div className="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
            <span className="relative inline-flex">
              <span className="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-primary opacity-75 -left-4 top-1/2 -translate-y-1/2"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-primary -left-4 top-1/2 -translate-y-1/2"></span>
            </span>
            Featured Tools
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">Your Go-To <span className="text-gradient">Image Toolkit</span></h2>
          <p className="text-muted-foreground text-lg max-w-2xl text-center mb-6">
            From conversion and resizing to compression and generation – all the image tools you need, right in your browser.
          </p>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
          {popularTools.slice(0, 6).map((tool) => (
            <Link key={tool.href} href={tool.href} className="group">
              <div className="bg-gradient-card rounded-xl overflow-hidden shadow-card border border-accent/30 h-full card-hover transition-all duration-300 hover:shadow-lg hover:scale-[1.02] hover:border-primary/30">
                <div className="p-6">
                  {/* Tool Icon */}
                  <div className="rounded-xl bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                    {tool.icon}
                  </div>

                  {/* Tool Header */}
                  <div className="mb-3">
                    <h3 className="text-xl font-bold group-hover:text-primary transition-colors">{tool.name}</h3>
                  </div>

                  {/* Tool Description */}
                  <p className="text-muted-foreground mb-4 text-sm leading-relaxed">
                    {tool.description}
                  </p>

                  {/* Call to Action */}
                  <div className="flex items-center text-primary font-medium">
                    Try Now <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* View All Tools Button */}
        <div className="mt-16 flex justify-center">
          <Link href="/all-tools">
            <Button size="lg" className="btn-primary px-8 py-6 rounded-xl group">
              <span>View All Tools</span>
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
    </section>
  );
}