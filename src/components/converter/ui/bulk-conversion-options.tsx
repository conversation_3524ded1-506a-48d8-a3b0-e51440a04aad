"use client";

import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageFormat } from '@/lib/constants';

interface BulkConversionOptionsProps {
  targetFormat: ImageFormat;
  onTargetFormatChange: (format: ImageFormat) => void;
  quality: number;
  onQualityChange: (quality: number) => void;
  disabled?: boolean;
}

const SUPPORTED_FORMATS: { value: ImageFormat; label: string; description: string }[] = [
  { value: 'jpg', label: 'JPG', description: 'Best for photos, smaller file size' },
  { value: 'png', label: 'PNG', description: 'Lossless quality, supports transparency' },
  { value: 'webp', label: 'WebP', description: 'Modern format, excellent compression' },
  { value: 'avif', label: 'AVIF', description: 'Next-gen format, best compression' },
  { value: 'bmp', label: 'BMP', description: 'Uncompressed, large file size' },
  { value: 'tiff', label: 'TIFF', description: 'Professional format, high quality' },
  { value: 'ico', label: 'ICO', description: 'Icon format for favicons' },
  { value: 'pdf', label: 'PDF', description: 'Document format' }
];

export function BulkConversionOptions({
  targetFormat,
  onTargetFormatChange,
  quality,
  onQualityChange,
  disabled = false
}: BulkConversionOptionsProps) {
  const selectedFormat = SUPPORTED_FORMATS.find(f => f.value === targetFormat);
  const showQualitySlider = ['jpg', 'jpeg', 'webp', 'avif'].includes(targetFormat);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Conversion Settings</CardTitle>
        <p className="text-sm text-muted-foreground">
          These settings will be applied to all uploaded images
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Target Format Selection */}
        <div className="space-y-2">
          <Label htmlFor="target-format">Convert to Format</Label>
          <Select
            value={targetFormat}
            onValueChange={(value) => onTargetFormatChange(value as ImageFormat)}
            disabled={disabled}
          >
            <SelectTrigger id="target-format">
              <SelectValue placeholder="Select target format" />
            </SelectTrigger>
            <SelectContent>
              {SUPPORTED_FORMATS.map((format) => (
                <SelectItem key={format.value} value={format.value}>
                  <div className="flex flex-col">
                    <span className="font-medium">{format.label}</span>
                    <span className="text-xs text-muted-foreground">
                      {format.description}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedFormat && (
            <p className="text-xs text-muted-foreground">
              {selectedFormat.description}
            </p>
          )}
        </div>

        {/* Quality Slider (for lossy formats) */}
        {showQualitySlider && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="quality-slider">Quality</Label>
              <span className="text-sm text-muted-foreground">{quality}%</span>
            </div>
            <Slider
              id="quality-slider"
              min={10}
              max={100}
              step={5}
              value={[quality]}
              onValueChange={(value) => onQualityChange(value[0])}
              disabled={disabled}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Smaller file</span>
              <span>Better quality</span>
            </div>
          </div>
        )}

        {/* Format-specific notes */}
        <div className="text-xs text-muted-foreground space-y-1">
          {targetFormat === 'png' && (
            <p>• PNG format preserves transparency and provides lossless compression</p>
          )}
          {targetFormat === 'jpg' && (
            <p>• JPG format is ideal for photos but doesn't support transparency</p>
          )}
          {targetFormat === 'webp' && (
            <p>• WebP provides excellent compression with good browser support</p>
          )}
          {targetFormat === 'avif' && (
            <p>• AVIF offers the best compression but has limited browser support</p>
          )}
          {targetFormat === 'ico' && (
            <p>• ICO format will generate multiple sizes (16px, 32px, 48px) for favicons</p>
          )}
          {targetFormat === 'pdf' && (
            <p>• PDF format will create a document with each image on a separate page</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
