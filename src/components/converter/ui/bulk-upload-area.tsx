"use client";

import React, { useCallback, useState } from 'react';
import { Upload, X, FileImage, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { formatBytes } from '@/lib/utils';

interface BulkUploadAreaProps {
  files: File[];
  onFilesChange: (files: File[]) => void;
  maxFiles?: number;
  maxFileSize?: number;
  acceptedTypes?: string[];
  disabled?: boolean;
}

const DEFAULT_ACCEPTED_TYPES = [
  'image/png',
  'image/jpeg', 
  'image/jpg',
  'image/webp',
  'image/avif',
  'image/bmp',
  'image/tiff'
];

export function BulkUploadArea({
  files,
  onFilesChange,
  maxFiles = 20,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
  disabled = false
}: BulkUploadAreaProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateFiles = useCallback((filesToValidate: FileList | File[]): File[] => {
    const fileArray = Array.from(filesToValidate);
    const validFiles: File[] = [];
    let errorMessage = '';

    // Check total number of files
    if (files.length + fileArray.length > maxFiles) {
      errorMessage = `Maximum ${maxFiles} files allowed. You're trying to add ${fileArray.length} files but already have ${files.length}.`;
      setError(errorMessage);
      return [];
    }

    for (const file of fileArray) {
      // Check file type
      if (!acceptedTypes.includes(file.type)) {
        errorMessage = `File "${file.name}" has unsupported format. Only image files are allowed.`;
        continue;
      }

      // Check file size
      if (file.size > maxFileSize) {
        errorMessage = `File "${file.name}" exceeds ${formatBytes(maxFileSize)} limit.`;
        continue;
      }

      // Check for duplicates
      const isDuplicate = files.some(existingFile => 
        existingFile.name === file.name && existingFile.size === file.size
      );
      
      if (isDuplicate) {
        errorMessage = `File "${file.name}" is already added.`;
        continue;
      }

      validFiles.push(file);
    }

    if (errorMessage && validFiles.length === 0) {
      setError(errorMessage);
    } else if (errorMessage && validFiles.length > 0) {
      setError(`Some files were skipped: ${errorMessage}`);
    } else {
      setError(null);
    }

    return validFiles;
  }, [files, maxFiles, maxFileSize, acceptedTypes]);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragging(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      const validFiles = validateFiles(droppedFiles);
      if (validFiles.length > 0) {
        onFilesChange([...files, ...validFiles]);
      }
    }
  }, [disabled, files, onFilesChange, validateFiles]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      const validFiles = validateFiles(selectedFiles);
      if (validFiles.length > 0) {
        onFilesChange([...files, ...validFiles]);
      }
    }

    // Reset input value to allow selecting the same files again
    e.target.value = '';
  }, [disabled, files, onFilesChange, validateFiles]);

  const removeFile = useCallback((indexToRemove: number) => {
    const updatedFiles = files.filter((_, index) => index !== indexToRemove);
    onFilesChange(updatedFiles);
    setError(null);
  }, [files, onFilesChange]);

  const clearAllFiles = useCallback(() => {
    onFilesChange([]);
    setError(null);
  }, [onFilesChange]);

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={cn(
          'relative flex flex-col items-center justify-center w-full min-h-32 border-2 border-dashed rounded-lg transition-all duration-300 cursor-pointer',
          isDragging
            ? 'border-primary bg-primary/10 scale-[1.02]'
            : 'border-gray-300 hover:border-primary/50 hover:bg-accent/5',
          disabled && 'opacity-50 cursor-not-allowed',
          error && 'border-destructive/50'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && document.getElementById('bulk-file-input')?.click()}
      >
        <input
          id="bulk-file-input"
          type="file"
          multiple
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          disabled={disabled}
        />
        
        <div className="flex flex-col items-center justify-center p-6 text-center">
          <Upload
            className={cn(
              'h-10 w-10 text-primary mb-3 transition-transform',
              isDragging && 'scale-110',
              disabled && 'text-muted-foreground'
            )}
          />
          <p className="text-lg font-medium text-primary mb-2">
            {isDragging ? 'Drop Images Here' : 'Choose Multiple Images'}
          </p>
          <p className="text-sm text-muted-foreground mb-2">
            Drag and drop up to {maxFiles} images here or click to browse
          </p>
          <p className="text-xs text-muted-foreground">
            Supports PNG, JPG, WebP, AVIF, BMP, TIFF • Max {formatBytes(maxFileSize)} per file
          </p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
          <AlertCircle className="h-4 w-4 text-destructive flex-shrink-0" />
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}
    </div>
  );
}
