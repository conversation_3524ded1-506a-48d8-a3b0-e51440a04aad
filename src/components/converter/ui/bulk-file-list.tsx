"use client";

import React from 'react';
import { X, FileImage, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { formatBytes } from '@/lib/utils';

export interface FileConversionStatus {
  status: 'pending' | 'converting' | 'completed' | 'error';
  progress?: number;
  error?: string;
  convertedBlob?: Blob;
  convertedFilename?: string;
}

interface BulkFileListProps {
  files: File[];
  fileStatuses: Record<string, FileConversionStatus>;
  onRemoveFile: (index: number) => void;
  onClearAll: () => void;
  disabled?: boolean;
}

function getFileKey(file: File, index: number): string {
  return `${file.name}-${file.size}-${index}`;
}

function FileStatusIcon({ status, progress }: { status: FileConversionStatus['status']; progress?: number }) {
  switch (status) {
    case 'pending':
      return <FileImage className="h-4 w-4 text-muted-foreground" />;
    case 'converting':
      return <Loader2 className="h-4 w-4 text-primary animate-spin" />;
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'error':
      return <AlertCircle className="h-4 w-4 text-destructive" />;
    default:
      return <FileImage className="h-4 w-4 text-muted-foreground" />;
  }
}

function FileStatusText({ status, progress, error }: FileConversionStatus) {
  switch (status) {
    case 'pending':
      return <span className="text-muted-foreground">Ready to convert</span>;
    case 'converting':
      return (
        <span className="text-primary">
          Converting... {progress !== undefined ? `${Math.round(progress)}%` : ''}
        </span>
      );
    case 'completed':
      return <span className="text-green-600">Converted successfully</span>;
    case 'error':
      return <span className="text-destructive">Error: {error || 'Conversion failed'}</span>;
    default:
      return <span className="text-muted-foreground">Unknown status</span>;
  }
}

export function BulkFileList({
  files,
  fileStatuses,
  onRemoveFile,
  onClearAll,
  disabled = false
}: BulkFileListProps) {
  if (files.length === 0) {
    return null;
  }

  const completedCount = Object.values(fileStatuses).filter(status => status.status === 'completed').length;
  const errorCount = Object.values(fileStatuses).filter(status => status.status === 'error').length;
  const convertingCount = Object.values(fileStatuses).filter(status => status.status === 'converting').length;

  return (
    <div className="space-y-4">
      {/* Header with summary and clear all button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>{files.length} files selected</span>
          {completedCount > 0 && (
            <span className="text-green-600">{completedCount} completed</span>
          )}
          {errorCount > 0 && (
            <span className="text-destructive">{errorCount} failed</span>
          )}
          {convertingCount > 0 && (
            <span className="text-primary">{convertingCount} converting</span>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onClearAll}
          disabled={disabled}
          className="text-xs"
        >
          Clear All
        </Button>
      </div>

      {/* File list */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {files.map((file, index) => {
          const fileKey = getFileKey(file, index);
          const status = fileStatuses[fileKey] || { status: 'pending' };
          
          return (
            <div
              key={fileKey}
              className={cn(
                'flex items-center gap-3 p-3 bg-card border rounded-lg transition-colors',
                status.status === 'completed' && 'border-green-200 bg-green-50/50',
                status.status === 'error' && 'border-destructive/20 bg-destructive/5',
                status.status === 'converting' && 'border-primary/20 bg-primary/5'
              )}
            >
              {/* File icon and status */}
              <div className="flex-shrink-0">
                <FileStatusIcon status={status.status} progress={status.progress} />
              </div>

              {/* File info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <p className="text-sm font-medium truncate">{file.name}</p>
                  <span className="text-xs text-muted-foreground flex-shrink-0">
                    {formatBytes(file.size)}
                  </span>
                </div>
                <div className="text-xs mt-1">
                  <FileStatusText {...status} />
                </div>
                
                {/* Progress bar for converting files */}
                {status.status === 'converting' && status.progress !== undefined && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-primary h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${status.progress}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Remove button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveFile(index)}
                disabled={disabled || status.status === 'converting'}
                className="flex-shrink-0 h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          );
        })}
      </div>
    </div>
  );
}
