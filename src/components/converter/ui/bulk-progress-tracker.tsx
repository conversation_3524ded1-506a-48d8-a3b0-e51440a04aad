"use client";

import React from 'react';
import { CheckCircle, AlertCircle, Loader2, Download } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

interface BulkProgressTrackerProps {
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  isConverting: boolean;
  onDownloadAll?: () => void;
  onRetryFailed?: () => void;
  estimatedTimeRemaining?: number;
}

function formatTime(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
}

export function BulkProgressTracker({
  totalFiles,
  completedFiles,
  failedFiles,
  isConverting,
  onDownloadAll,
  onRetryFailed,
  estimatedTimeRemaining
}: BulkProgressTrackerProps) {
  if (totalFiles === 0) {
    return null;
  }

  const processedFiles = completedFiles + failedFiles;
  const progressPercentage = totalFiles > 0 ? (processedFiles / totalFiles) * 100 : 0;
  const isComplete = processedFiles === totalFiles;
  const hasFailures = failedFiles > 0;
  const hasSuccesses = completedFiles > 0;

  return (
    <div className="space-y-4 p-4 bg-card border rounded-lg">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Conversion Progress</h3>
        {isConverting && (
          <div className="flex items-center gap-2 text-sm text-primary">
            <Loader2 className="h-4 w-4 animate-spin" />
            Converting...
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <Progress value={progressPercentage} className="h-2" />
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            {processedFiles} of {totalFiles} files processed
          </span>
          {isConverting && estimatedTimeRemaining && (
            <span>
              ~{formatTime(estimatedTimeRemaining)} remaining
            </span>
          )}
        </div>
      </div>

      {/* Status Summary */}
      <div className="flex items-center gap-4 text-sm">
        {hasSuccesses && (
          <div className="flex items-center gap-1 text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span>{completedFiles} completed</span>
          </div>
        )}
        {hasFailures && (
          <div className="flex items-center gap-1 text-destructive">
            <AlertCircle className="h-4 w-4" />
            <span>{failedFiles} failed</span>
          </div>
        )}
        {isConverting && (
          <div className="flex items-center gap-1 text-primary">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>{totalFiles - processedFiles} remaining</span>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      {isComplete && (
        <div className="flex items-center gap-2 pt-2 border-t">
          {hasSuccesses && onDownloadAll && (
            <Button
              onClick={onDownloadAll}
              className="flex items-center gap-2"
              size="sm"
            >
              <Download className="h-4 w-4" />
              Download All ({completedFiles} files)
            </Button>
          )}
          {hasFailures && onRetryFailed && (
            <Button
              variant="outline"
              onClick={onRetryFailed}
              className="flex items-center gap-2"
              size="sm"
            >
              <AlertCircle className="h-4 w-4" />
              Retry Failed ({failedFiles} files)
            </Button>
          )}
        </div>
      )}

      {/* Completion Message */}
      {isComplete && (
        <div className={cn(
          'p-3 rounded-lg text-sm',
          hasFailures && !hasSuccesses && 'bg-destructive/10 text-destructive',
          hasSuccesses && !hasFailures && 'bg-green-50 text-green-700',
          hasSuccesses && hasFailures && 'bg-yellow-50 text-yellow-700'
        )}>
          {!hasFailures && hasSuccesses && (
            <span>🎉 All files converted successfully!</span>
          )}
          {hasFailures && !hasSuccesses && (
            <span>❌ All conversions failed. Please check your files and try again.</span>
          )}
          {hasFailures && hasSuccesses && (
            <span>
              ⚠️ Conversion completed with {failedFiles} failures. 
              {completedFiles} files were converted successfully.
            </span>
          )}
        </div>
      )}
    </div>
  );
}
