import React from 'react';
import { ArrowRight, FileImage, FileText, Palette, FileIcon, Image, File, Search, Shield, UserX, Star, Shrink, Crop as CropIcon, Layers, Square, Wrench, Scissors, Binary, SlidersHorizontal, TextSelect } from "lucide-react";
import { Tool, ToolCategory } from '@/types/tools'; // Import the types

// Define tool categories
export const toolCategories: ToolCategory[] = [
  {
    name: "PNG Conversion",
    icon: <FileImage className="h-4 w-4 text-primary" aria-label="PNG Conversion category icon" />,
    tools: [
      {
        name: "PNG to JPG",
        href: "/convert/png-to-jpg",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="PNG to JPG icon" />,
        description: "Convert PNG images to JPG format with adjustable quality. Perfect for reducing file size while maintaining good visual quality.",
        relatedHrefs: [
          "/compress/jpg",
          "/resize/jpg",
          "/convert/jpg-to-png",
          "/convert/png-to-webp"
        ]
      },
      {
        name: "PNG to WebP",
        href: "/convert/png-to-webp",
        icon: <Image className="h-6 w-6 text-primary" aria-label="PNG to WebP icon" />,
        description: "Convert PNG to WebP format for superior compression and quality. Ideal for web use with smaller file sizes.",
        relatedHrefs: [
          "/compress/webp",
          "/resize/webp",
          "/convert/webp-to-png",
          "/convert/png-to-jpg"
        ]
      },
      {
        name: "PNG to AVIF",
        href: "/convert/png-to-avif",
        icon: <File className="h-6 w-6 text-primary" aria-label="PNG to AVIF icon" />,
        description: "Convert PNG to AVIF format for next-generation compression. Get the smallest file sizes with excellent quality.",
        relatedHrefs: [
          "/compress/avif",
          "/resize-image",
          "/convert/avif-to-png",
          "/convert/png-to-webp"
        ]
      },
      {
        name: "PNG to PDF",
        href: "/convert/png-to-pdf",
        icon: <FileText className="h-6 w-6 text-primary" aria-label="PNG to PDF icon" />,
        description: "Convert PNG images to PDF documents with customizable page settings. Perfect for creating professional documents from your images.",
        relatedHrefs: [
          "/convert/pdf-to-png",
          "/convert/png-to-jpg",
          "/convert/png-to-webp",
          "/compress/pdf"
        ]
      },
      {
        name: "PNG to BMP",
        href: "/convert/png-to-bmp",
        icon: <FileIcon className="h-6 w-6 text-primary" aria-label="PNG to BMP icon" />,
        description: "Convert PNG to BMP format for maximum compatibility with legacy systems and software that require bitmap images.",
        relatedHrefs: [
          "/convert/bmp-to-png",
          "/convert/png-to-jpg",
          "/compress/bmp",
          "/resize-image"
        ]
      },
      {
        name: "PNG to TIFF",
        href: "/convert/png-to-tiff",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="PNG to TIFF icon" />,
        description: "Convert PNG to TIFF format for professional use. Ideal for printing, publishing, and archiving high-quality images.",
        relatedHrefs: [
          "/convert/tiff-to-png",
          "/convert/png-to-jpg",
          "/compress/tiff",
          "/resize-image"
        ]
      }
    ]
  },
  {
    name: "JPG Conversion",
    icon: <FileImage className="h-4 w-4 text-primary" aria-label="JPG Conversion category icon" />,
    tools: [
      {
        name: "JPG to PNG",
        href: "/convert/jpg-to-png",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="JPG to PNG icon" />,
        description: "Convert JPG images to PNG format with lossless quality. Perfect for preserving image quality and adding transparency support.",
        relatedHrefs: [
          "/compress/png",
          "/resize/png",
          "/convert/png-to-jpg",
          "/convert/jpg-to-webp"
        ]
      },
      {
        name: "JPG to WebP",
        href: "/convert/jpg-to-webp",
        icon: <Image className="h-6 w-6 text-primary" aria-label="JPG to WebP icon" />,
        description: "Convert JPG to WebP format for optimized web images. Offers smaller file sizes than JPG with excellent quality.",
        relatedHrefs: [
          "/compress/webp",
          "/resize/webp",
          "/convert/webp-to-jpg",
          "/convert/jpg-to-png"
        ]
      },
      {
        name: "JPG to AVIF",
        href: "/convert/jpg-to-avif",
        icon: <File className="h-6 w-6 text-primary" aria-label="JPG to AVIF icon" />,
        description: "Convert JPG to AVIF format for superior compression. Leverage next-gen tech for smaller files than JPG or WebP.",
        relatedHrefs: [
          "/compress/avif",
          "/resize-image",
          "/convert/avif-to-jpg",
          "/convert/jpg-to-webp"
        ]
      },
      {
        name: "JPG to PDF",
        href: "/convert/jpg-to-pdf",
        icon: <FileText className="h-6 w-6 text-primary" aria-label="JPG to PDF icon" />,
        description: "Convert JPG images or photos into a single PDF file. Customize page layout for documents, portfolios, or easy sharing.",
        relatedHrefs: [
          "/convert/pdf-to-jpg",
          "/convert/jpg-to-png",
          "/compress/pdf",
          "/resize-image"
        ]
      },
      {
        name: "JPG to BMP",
        href: "/convert/jpg-to-bmp",
        icon: <FileIcon className="h-6 w-6 text-primary" aria-label="JPG to BMP icon" />,
        description: "Convert JPG images to BMP for lossless quality, ideal for legacy applications or simple storage.",
        relatedHrefs: [
          "/convert/bmp-to-jpg",
          "/convert/jpg-to-png",
          "/compress/bmp",
          "/resize-image"
        ]
      },
      {
        name: "JPG to TIFF",
        href: "/convert/jpg-to-tiff",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="JPG to TIFF icon" />,
        description: "Convert JPG to TIFF format for professional applications. Perfect for preserving photos in high quality for printing and archiving.",
        relatedHrefs: [
          "/convert/tiff-to-jpg",
          "/convert/jpg-to-png",
          "/compress/tiff",
          "/resize-image"
        ]
      }
    ]
  },
  {
    name: "WebP Conversion",
    icon: <Image className="h-4 w-4 text-primary" aria-label="WebP Conversion category icon" />,
    tools: [
      {
        name: "WebP to PNG",
        href: "/convert/webp-to-png",
        icon: <Image className="h-6 w-6 text-primary" aria-label="WebP to PNG icon" />,
        description: "Convert WebP images to PNG format while preserving transparency and quality. Perfect for web graphics and lossless compression.",
        relatedHrefs: [
          "/compress/png",
          "/resize/png",
          "/convert/png-to-webp",
          "/convert/webp-to-jpg"
        ]
      },
      {
        name: "WebP to JPG",
        href: "/convert/webp-to-jpg",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="WebP to JPG icon" />,
        description: "Convert WebP images to JPG format quickly and easily. Ideal for ensuring compatibility across various platforms and devices.",
        relatedHrefs: [
          "/compress/jpg",
          "/resize/jpg",
          "/convert/jpg-to-webp",
          "/convert/webp-to-png"
        ]
      },
      {
        name: "WebP to AVIF",
        href: "/convert/webp-to-avif",
        icon: <File className="h-6 w-6 text-primary" aria-label="WebP to AVIF icon" />,
        description: "Convert WebP images to AVIF for even better compression. The latest in image format technology.",
        relatedHrefs: [
          "/compress/avif",
          "/resize-image",
          "/convert/avif-to-webp",
          "/convert/webp-to-jpg"
        ]
      },
      {
        name: "WebP to PDF",
        href: "/convert/webp-to-pdf",
        icon: <FileText className="h-6 w-6 text-primary" aria-label="WebP to PDF icon" />,
        description: "Convert WebP images into a single PDF document. Customize page size and orientation for your needs. Ideal for compiling images.",
        relatedHrefs: [
          "/convert/pdf-to-webp",
          "/convert/webp-to-jpg",
          "/compress/pdf",
          "/resize-image"
        ]
      },
      {
        name: "WebP to BMP",
        href: "/convert/webp-to-bmp",
        icon: <FileIcon className="h-6 w-6 text-primary" aria-label="WebP to BMP icon" />,
        description: "Convert WebP images to BMP format effortlessly. Suitable for applications requiring uncompressed bitmap images.",
        relatedHrefs: [
          "/convert/bmp-to-webp",
          "/convert/webp-to-jpg",
          "/compress/bmp",
          "/resize-image"
        ]
      },
      {
        name: "WebP to TIFF",
        href: "/convert/webp-to-tiff",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="WebP to TIFF icon" />,
        description: "Convert WebP to TIFF format for professional use. Transform web-optimized images into high-quality files for printing and archiving.",
        relatedHrefs: [
          "/convert/tiff-to-webp",
          "/convert/webp-to-jpg",
          "/compress/tiff",
          "/resize-image"
        ]
      }
    ]
  },
  {
    name: "AVIF Conversion",
    icon: <File className="h-4 w-4 text-primary" aria-label="AVIF Conversion category icon" />,
    tools: [
      {
        name: "AVIF to PNG",
        href: "/convert/avif-to-png",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="AVIF to PNG icon" />,
        description: "Convert AVIF to PNG format with lossless quality. Transform next-gen AVIF files into PNG images with transparency support.",
        relatedHrefs: [
          "/compress/png",
          "/resize/png",
          "/convert/png-to-avif",
          "/convert/avif-to-jpg"
        ]
      },
      {
        name: "AVIF to JPG",
        href: "/convert/avif-to-jpg",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="AVIF to JPG icon" />,
        description: "Convert AVIF to JPG format for wider compatibility. Transform next-gen AVIF files into widely supported JPG images.",
        relatedHrefs: [
          "/compress/jpg",
          "/resize/jpg",
          "/convert/avif-to-png",
          "/convert/jpg-to-avif"
        ]
      },
      {
        name: "AVIF to WebP",
        href: "/convert/avif-to-webp",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="AVIF to WebP icon" />,
        description: "Convert AVIF to WebP format for broader browser support. Transform next-gen AVIF files into widely supported WebP images.",
        relatedHrefs: [
          "/compress/webp",
          "/resize/webp",
          "/convert/webp-to-avif",
          "/convert/avif-to-png"
        ]
      },
      {
        name: "AVIF to TIFF",
        href: "/convert/avif-to-tiff",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="AVIF to TIFF icon" />,
        description: "Convert AVIF to TIFF format for professional applications. Transform next-gen web images into high-quality files for printing and archiving.",
        relatedHrefs: [
          "/compress/tiff",
          "/resize-image",
          "/convert/tiff-to-avif",
          "/convert/avif-to-png"
        ]
      }
    ]
  },
  {
    name: "TIFF Conversion",
    icon: <FileImage className="h-4 w-4 text-primary" aria-label="TIFF Conversion category icon" />,
    tools: [
      {
        name: "TIFF to PNG",
        href: "/convert/tiff-to-png",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="TIFF to PNG icon" />,
        description: "Convert TIFF to PNG format with lossless quality. Transform professional TIFF files into web-friendly PNG images with transparency support.",
        relatedHrefs: [
          "/compress/png",
          "/resize/png",
          "/convert/png-to-tiff",
          "/convert/tiff-to-jpg"
        ]
      },
      {
        name: "TIFF to JPG",
        href: "/convert/tiff-to-jpg",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="TIFF to JPG icon" />,
        description: "Convert TIFF to JPG format with adjustable quality. Transform professional TIFF files into web-friendly JPG images for easier sharing.",
        relatedHrefs: [
          "/compress/jpg",
          "/resize/jpg",
          "/convert/jpg-to-tiff",
          "/convert/tiff-to-png"
        ]
      },
      {
        name: "TIFF to WebP",
        href: "/convert/tiff-to-webp",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="TIFF to WebP icon" />,
        description: "Convert TIFF to WebP format for web optimization. Transform professional TIFF files into modern WebP images with superior compression.",
        relatedHrefs: [
          "/compress/webp",
          "/resize/webp",
          "/convert/webp-to-tiff",
          "/convert/tiff-to-png"
        ]
      },
      {
        name: "TIFF to AVIF",
        href: "/convert/tiff-to-avif",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="TIFF to AVIF icon" />,
        description: "Convert TIFF to AVIF format for next-gen web optimization. Transform professional TIFF files into cutting-edge AVIF images.",
        relatedHrefs: [
          "/compress/avif",
          "/resize-image",
          "/convert/avif-to-tiff",
          "/convert/tiff-to-png"
        ]
      }
    ]
  },
  {
    name: "BMP Conversion",
    icon: <FileImage className="h-4 w-4 text-primary" aria-label="BMP Conversion category icon" />,
    tools: [
      {
        name: "BMP to PNG",
        href: "/convert/bmp-to-png",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="BMP to PNG icon" />,
        description: "Convert BMP images to PNG format. Retain quality with lossless compression and add transparency support.",
        relatedHrefs: [
          "/compress/png",
          "/resize/png",
          "/convert/png-to-bmp",
          "/convert/bmp-to-jpg"
        ]
      },
      {
        name: "BMP to JPG",
        href: "/convert/bmp-to-jpg",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="BMP to JPG icon" />,
        description: "Convert BMP images to JPG format. Reduce file size significantly for web use while maintaining good quality.",
        relatedHrefs: [
          "/compress/jpg",
          "/resize/jpg",
          "/convert/jpg-to-bmp",
          "/convert/bmp-to-png"
        ]
      },
      {
        name: "BMP to WebP",
        href: "/convert/bmp-to-webp",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="BMP to WebP icon" />,
        description: "Convert BMP images to WebP format. Benefit from smaller file sizes and excellent quality for web use.",
        relatedHrefs: [
          "/compress/webp",
          "/resize/webp",
          "/convert/webp-to-bmp",
          "/convert/bmp-to-png"
        ]
      },
      {
        name: "BMP to AVIF",
        href: "/convert/bmp-to-avif",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="BMP to AVIF icon" />,
        description: "Convert BMP images to AVIF format. Leverage next-gen compression for superior quality at smaller file sizes.",
        relatedHrefs: [
          "/compress/avif",
          "/resize-image",
          "/convert/avif-to-bmp",
          "/convert/bmp-to-png"
        ]
      },
      {
        name: "BMP to PDF",
        href: "/convert/bmp-to-pdf",
        icon: <FileText className="h-6 w-6 text-primary" aria-label="BMP to PDF icon" />,
        description: "Convert BMP images into PDF documents. Easy way to archive or share bitmap images in a standard format.",
        relatedHrefs: [
          "/convert/pdf-to-bmp",
          "/convert/bmp-to-png",
          "/compress/pdf",
          "/resize-image"
        ]
      }
    ]
  },
  {
    name: "Image Compression",
    icon: <Shrink className="h-4 w-4 text-primary" aria-label="Image Compression category icon" />,
    tools: [
      {
        name: "Compress Any Image",
        href: "/compress-image",
        icon: <Shrink className="h-6 w-6 text-primary" aria-label="Compress Any Image icon" />,
        description: "Compress JPG, PNG, WebP, GIF, etc. Reduce file size while maintaining quality.",
        relatedHrefs: [
          "/resize-image",
          "/convert/png-to-webp",
          "/convert/jpg-to-webp",
          "/crop-image"
        ]
      },
      {
        name: "Compress PNG",
        href: "/compress/png",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="Compress PNG icon" />,
        description: "Reduce PNG file size while maintaining transparency and quality. Optimize your PNGs for web use.",
        relatedHrefs: [
          "/compress-image",
          "/resize/png",
          "/convert/png-to-jpg",
          "/convert/png-to-webp"
        ]
      },
      {
        name: "Compress JPG",
        href: "/compress/jpg",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="Compress JPG icon" />,
        description: "Reduce JPG file size significantly with adjustable quality settings. Optimize photos and web images.",
        relatedHrefs: [
          "/compress-image",
          "/resize/jpg",
          "/convert/jpg-to-png",
          "/convert/jpg-to-webp"
        ]
      },
      {
        name: "Compress WebP",
        href: "/compress/webp",
        icon: <Image className="h-6 w-6 text-primary" aria-label="Compress WebP icon" />,
        description: "Further reduce WebP file size while preserving quality. Optimize modern images for maximum performance.",
        relatedHrefs: [
          "/compress-image",
          "/resize/webp",
          "/convert/webp-to-png",
          "/convert/webp-to-jpg"
        ]
      }
    ]
  },
  {
    name: "Image Resizing",
    icon: <Scissors className="h-4 w-4 text-primary" aria-label="Image Resizing category icon" />,
    tools: [
      {
        name: "Resize Any Image",
        href: "/resize-image",
        icon: <Scissors className="h-6 w-6 text-primary" aria-label="Resize Any Image icon" />,
        description: "Resize JPG, PNG, WebP, GIF, etc. Change dimensions by pixels or percentage.",
        relatedHrefs: [
          "/compress-image",
          "/crop-image",
          "/convert/png-to-jpg",
          "/favicon-maker"
        ]
      },
      {
        name: "Resize PNG",
        href: "/resize/png",
        icon: <Image className="h-6 w-6 text-primary" aria-label="Resize PNG icon" />,
        description: "Change the dimensions (width and height) of your PNG images while preserving transparency.",
        relatedHrefs: [
          "/resize-image",
          "/compress/png",
          "/convert/png-to-jpg",
          "/convert/png-to-webp"
        ]
      },
      {
        name: "Resize JPG",
        href: "/resize/jpg",
        icon: <FileImage className="h-6 w-6 text-primary" aria-label="Resize JPG icon" />,
        description: "Resize JPG/JPEG photos and images to specific pixel dimensions for web or print.",
        relatedHrefs: [
          "/resize-image",
          "/compress/jpg",
          "/convert/jpg-to-png",
          "/convert/jpg-to-webp"
        ]
      },
      {
        name: "Resize WebP",
        href: "/resize/webp",
        icon: <Image className="h-6 w-6 text-primary" aria-label="Resize WebP icon" />,
        description: "Adjust the size of your WebP images, perfect for optimizing modern web graphics.",
        relatedHrefs: [
          "/resize-image",
          "/compress/webp",
          "/convert/webp-to-png",
          "/convert/webp-to-jpg"
        ]
      }
    ]
  },
  {
    name: "Image Editing",
    icon: <Palette className="h-4 w-4 text-primary" aria-label="Image Editing category icon" />,
    tools: [
      {
        name: "Crop Image",
        href: "/crop-image",
        icon: <CropIcon className="h-6 w-6 text-primary" aria-label="Crop Image icon" />,
        description: "Easily crop your images to the perfect size and shape. Apply preset aspect ratios or make freeform crops.",
        relatedHrefs: [
          "/resize-image",
          "/rotate-flip-image",
          "/compress-image",
          "/favicon-maker"
        ]
      },
      {
        name: "Rotate & Flip Image",
        href: "/rotate-flip-image",
        icon: <Palette className="h-6 w-6 text-primary" aria-label="Rotate & Flip Image icon" />,
        description: "Easily rotate your image by degrees or preset angles, and flip it horizontally or vertically.",
        relatedHrefs: [
          "/crop-image",
          "/resize-image",
          "/compress-image",
          "/convert/png-to-jpg"
        ]
      },
      {
        name: "Image Adjustments",
        href: "/adjust-image",
        icon: <SlidersHorizontal className="h-6 w-6 text-primary" aria-label="Image Adjustments icon" />,
        description: "Apply basic image adjustments like Greyscale and Tint. More filters coming soon!",
        relatedHrefs: [
          "/crop-image",
          "/rotate-flip-image",
          "/compress-image",
          "/resize-image"
        ]
      },
      {
        name: "Add Watermark",
        href: "/add-watermark",
        icon: <TextSelect className="h-6 w-6 text-primary" aria-label="Add Watermark icon" />,
        description: "Easily add text watermarks to your photos and images online. Customize position, size, color, and opacity.",
        relatedHrefs: [
          "/composite-images",
          "/crop-image",
          "/resize-image",
          "/compress-image"
        ]
      },
      {
        name: "Image Compositor",
        href: "/composite-images",
        icon: <Layers className="h-6 w-6 text-primary" />,
        description: "Merge multiple images by overlaying them onto a base image. Adjust position, blend modes, and tiling.",
        relatedHrefs: [
          "/add-watermark",
          "/resize-image",
          "/compress-image",
          "/convert/png-to-jpg"
        ]
      }
    ]
  },
  {
    name: "Special Tools",
    icon: <Palette className="h-4 w-4 text-primary" aria-label="Special Tools category icon" />,
    tools: [
      {
        name: "Bulk Image Converter",
        href: "/bulk-convert",
        icon: <Layers className="h-6 w-6 text-primary" aria-label="Bulk Image Converter icon" />,
        description: "Convert multiple images at once. Upload up to 20 images and convert them all to your desired format with the same settings. Download as ZIP.",
        relatedHrefs: [
          "/convert/png-to-jpg",
          "/convert/jpg-to-webp",
          "/compress-image",
          "/resize-image"
        ]
      },
      {
        name: "Favicon Maker",
        href: "/favicon-maker",
        icon: <Palette className="h-6 w-6 text-primary" aria-label="Favicon Maker icon" />,
        description: "Create a favicon from your image. Supports various sizes and formats for perfect website branding.",
        relatedHrefs: [
          "/resize-image",
          "/crop-image",
          "/compress/png",
          "/convert/png-to-jpg"
        ]
      },
      {
        name: "Image Placeholder Maker",
        href: "/placeholder-maker",
        icon: <Square className="h-6 w-6 text-primary" aria-label="Image Placeholder Maker icon" />,
        description: "Quickly generate custom placeholder images with specific dimensions, colors, and text for prototypes or layouts.",
        relatedHrefs: [
          "/resize-image",
          "/crop-image",
          "/composite-images",
          "/convert/png-to-jpg"
        ]
      }
    ]
  },
  {
    name: "Encoding Tools",
    icon: <Binary className="h-4 w-4 text-primary" aria-label="Encoding Tools category icon" />,
    tools: [
      {
        name: "Image to Base64 / Base64 to Image",
        href: "/encode/image-to-base64",
        icon: <Binary className="h-6 w-6 text-primary" aria-label="Image to Base64 icon" />,
        description: "Convert images to Base64 strings for data URLs or embedding, and decode Base64 strings back into images.",
        relatedHrefs: [
          "/convert/png-to-jpg",
          "/convert/jpg-to-png",
          "/compress-image",
          "/resize-image"
        ]
      },
      // Add other encoding tools here in the future
    ]
  }
];

// Combine all tools into one list for easier access
export const allTools: Tool[] = toolCategories.reduce((acc, category) => {
  return acc.concat(category.tools);
}, [] as Tool[]); // Specify the initial accumulator type 